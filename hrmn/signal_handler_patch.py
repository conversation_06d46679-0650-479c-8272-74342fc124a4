#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Signal Handler Patch for Hailo Pose Estimation Scripts
Ensures clean Hailo device release on exit/crash

Add this to the beginning of pose_estimation.py and hand_pose_estimation.py:

import signal
import sys
import atexit

# Import this patch
from signal_handler_patch import setup_clean_exit

# Call setup at the beginning of main
setup_clean_exit()
"""

import signal
import sys
import atexit
import os

def clean_exit(signum=None, frame=None):
    """Clean exit handler that releases Hailo resources"""
    print(f"\n[CLEANUP] Signal {signum} received - cleaning up Hailo resources...")
    
    try:
        # Try to release Hailo resources using CLI
        os.system("hailortcli release-all 2>/dev/null || true")
        print("[CLEANUP] Hailo handles released")
    except Exception as e:
        print(f"[CLEANUP] Error releasing Hailo handles: {e}")
    
    try:
        # Clean up shared memory
        os.system("rm -f /dev/shm/pose_shm /dev/shm/hand_shm 2>/dev/null || true")
        print("[CLEANUP] Shared memory cleaned")
    except Exception as e:
        print(f"[CLEANUP] Error cleaning shared memory: {e}")
    
    print("[CLEANUP] Clean exit completed")
    
    if signum is not None:
        sys.exit(0)

def setup_clean_exit():
    """Set up signal handlers for clean exit"""
    # Handle common termination signals
    for sig in (signal.SIGINT, signal.SIGTERM, signal.SIGHUP):
        try:
            signal.signal(sig, clean_exit)
        except (OSError, ValueError):
            # Some signals might not be available on all platforms
            pass
    
    # Also register atexit handler as backup
    atexit.register(clean_exit)
    
    print("[SIGNAL] Clean exit handlers registered")

if __name__ == "__main__":
    print("This is a utility module - import it in your pose estimation scripts")
    print("Usage:")
    print("  from signal_handler_patch import setup_clean_exit")
    print("  setup_clean_exit()  # Call this at the beginning of your main function")
