#!/bin/bash

# =============================================================================
# ULTRA RESTART TEST SCRIPT
# Tests the improved start.sh script with multiple restart cycles
# =============================================================================

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_test() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_fail() {
    echo -e "${RED}[FAIL]${NC} $1"
}

print_info() {
    echo -e "${YELLOW}[INFO]${NC} $1"
}

# Test function
test_restart_cycle() {
    local cycle=$1
    print_test "=== RESTART CYCLE $cycle ==="
    
    # Stop everything first
    print_info "Stopping all processes..."
    ./start.sh --stop
    sleep 3
    
    # Verify clean state
    print_info "Verifying clean state..."
    if pgrep -f "pose_estimation.py" > /dev/null; then
        print_fail "Pose estimation still running after stop"
        return 1
    fi
    
    if pgrep -f "simple_pose_socket.py" > /dev/null; then
        print_fail "Socket server still running after stop"
        return 1
    fi
    
    if [ -f "/dev/shm/pose_shm" ]; then
        print_fail "Shared memory still exists after stop"
        return 1
    fi
    
    # Check Hailo status
    if command -v hailortcli > /dev/null; then
        OPEN_HANDLES=$(hailortcli list-handles 2>/dev/null | grep -c "Handle" || echo "0")
        if [ "$OPEN_HANDLES" -gt 0 ]; then
            print_fail "Hailo handles still open after stop ($OPEN_HANDLES)"
            return 1
        fi
        
        if ! hailortcli scan 2>/dev/null | grep -q "Hailo"; then
            print_fail "Hailo device not detected"
            return 1
        fi
    fi
    
    print_success "Clean state verified"
    
    # Start system
    print_info "Starting system..."
    if ./start.sh --performance --no-browser; then
        print_success "System started successfully"
        
        # Wait a bit and check if everything is running
        sleep 5
        
        # Check processes
        if ! pgrep -f "pose_estimation.py" > /dev/null; then
            print_fail "Pose estimation not running"
            return 1
        fi
        
        if ! pgrep -f "simple_pose_socket.py" > /dev/null; then
            print_fail "Socket server not running"
            return 1
        fi
        
        if [ ! -f "/dev/shm/pose_shm" ]; then
            print_fail "Shared memory not created"
            return 1
        fi
        
        print_success "All processes running correctly"
        
        # Check for errors in logs
        if grep -q "HAILO_OUT_OF_PHYSICAL_DEVICES" backend.log; then
            print_fail "Hailo device busy error in logs"
            return 1
        fi
        
        if grep -q "Device or resource busy" backend.log; then
            print_fail "Camera busy error in logs"
            return 1
        fi
        
        if grep -q "Segmentation fault" backend.log; then
            print_fail "Segmentation fault in logs"
            return 1
        fi
        
        print_success "No critical errors in logs"
        print_success "Cycle $cycle completed successfully!"
        return 0
    else
        print_fail "System failed to start"
        return 1
    fi
}

# Main test
print_test "🔥 ULTRA RESTART TEST STARTING 🔥"
print_info "This will test multiple restart cycles to verify the fixes"
echo ""

# Initial cleanup
print_info "Performing initial cleanup..."
./start.sh --stop
sleep 5

TOTAL_CYCLES=3
SUCCESSFUL_CYCLES=0

for i in $(seq 1 $TOTAL_CYCLES); do
    if test_restart_cycle $i; then
        SUCCESSFUL_CYCLES=$((SUCCESSFUL_CYCLES + 1))
    else
        print_fail "Cycle $i failed - stopping test"
        break
    fi
    
    if [ $i -lt $TOTAL_CYCLES ]; then
        print_info "Waiting 10 seconds before next cycle..."
        sleep 10
    fi
done

# Final cleanup
print_info "Performing final cleanup..."
./start.sh --stop

# Results
echo ""
print_test "=== TEST RESULTS ==="
print_info "Successful cycles: $SUCCESSFUL_CYCLES/$TOTAL_CYCLES"

if [ $SUCCESSFUL_CYCLES -eq $TOTAL_CYCLES ]; then
    print_success "🎉 ALL TESTS PASSED! 🎉"
    print_success "The restart fixes are working correctly!"
    exit 0
else
    print_fail "❌ SOME TESTS FAILED ❌"
    print_fail "Check the logs for more details"
    exit 1
fi
