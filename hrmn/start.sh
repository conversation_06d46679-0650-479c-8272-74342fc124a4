#!/bin/bash

# =============================================================================
# Raspberry Pi Pose Estimation & Hand Ninja Game Startup Script
# =============================================================================
# This script starts both the backend (pose estimation) and frontend (web server)
# Usage: ./start.sh [OPTIONS]
#
# Options:
#   --camera-input INPUT    Camera input (rpi, usb, /dev/videoX) [default: rpi]
#   --performance          Use fakesink (performance mode)
#   --display              Use autovideosink (display mode)
#   --stop                 Stop all running processes
#   --status               Show status of running processes
#   --help                 Show this help message
# =============================================================================

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Default settings
CAMERA_INPUT="rpi"
BACKEND_SCRIPT="./run_pose_estimation.sh"
FRONTEND_SCRIPT="python simple_pose_socket.py"
AUTO_STOP=true
OPEN_BROWSER=true

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[STARTUP]${NC} $1"
}

# Function to check if a process is running
check_process() {
    local name="$1"
    local pattern="$2"
    if pgrep -f "$pattern" > /dev/null; then
        print_status "$name is running (PID: $(pgrep -f "$pattern"))"
        return 0
    else
        print_warning "$name is not running"
        return 1
    fi
}

# Function to stop processes - ULTRA DEEP CLEANUP MODE
stop_processes() {
    print_header "🔥 ULTRA DEEP CLEANUP MODE ACTIVATED 🔥"

    # Wait a moment for system stability
    sleep 1

    # 1) Kill specific pose estimation processes first
    print_status "Killing pose estimation processes..."
    pkill -f "pose_estimation.py" && print_status "Pose estimation processes killed" || true
    pkill -f "hand_pose_estimation.py" && print_status "Hand pose estimation processes killed" || true
    pkill -f "simple_pose_socket.py" && print_status "Web server processes killed" || true
    pkill -f "hand_ninja_game.py" && print_status "Hand ninja game processes killed" || true
    pkill -f "my_new_game.py" && print_status "New game processes killed" || true

    # 2) Kill Python pose processes with extreme prejudice
    print_status "Killing remaining Python pose processes..."
    sudo pkill -9 -f "python.*pose" 2>/dev/null || true
    sudo pkill -9 -f "multiprocessing.resource_tracker" 2>/dev/null || true

    # 3) HAILO DEVICE ULTRA RESET - Community proven solution
    print_header "🚀 HAILO DEVICE ULTRA RESET 🚀"

    # First: Release all open handles using official HailoRT CLI
    print_status "Releasing all Hailo handles..."
    if command -v hailortcli > /dev/null; then
        # Check for open handles
        OPEN_HANDLES=$(hailortcli list-handles 2>/dev/null | grep -c "Handle" || echo "0")
        if [ "$OPEN_HANDLES" -gt 0 ]; then
            print_warning "Found $OPEN_HANDLES open Hailo handles - releasing..."
            hailortcli release-all 2>/dev/null || true
            sleep 2
        else
            print_status "No open Hailo handles found"
        fi
    fi

    # Kill all Hailo processes
    print_status "Terminating all Hailo processes..."
    HAILO_EXCLUSION="start.sh|switch_video_sink.sh|grep|hailo-tappas"

    while pgrep -a -f hailo | grep -Ev "$HAILO_EXCLUSION" >/dev/null 2>&1; do
        pgrep -a -f hailo | grep -Ev "$HAILO_EXCLUSION" | while read -r PID LINE; do
            if ps -o stat= -p "$PID" 2>/dev/null | grep -q 'Z'; then
                print_warning "Zombie process found: $PID -> $LINE"
                ZPPID="$(ps -o ppid= -p "$PID" 2>/dev/null)"
                if [[ "$ZPPID" -gt 1 ]] 2>/dev/null; then
                    print_status "Killing parent process $ZPPID of zombie $PID"
                    sudo kill -9 "$ZPPID" 2>/dev/null || true
                fi
            else
                print_status "Killing $PID: $LINE"
                sudo kill -9 "$PID" 2>/dev/null || true
            fi
        done
        sleep 1
    done

    # Stop Hailo services
    print_status "Stopping Hailo services..."
    sudo systemctl stop hailort 2>/dev/null || true
    sudo pkill -9 hailort_service 2>/dev/null || true

    # CRITICAL: PCIe Reset BEFORE module removal (community solution)
    print_status "Performing PCIe reset..."
    HAILO_PCI_DEVICE=$(lspci | awk '/Hailo/ {print $1}' | head -1)
    if [ -n "$HAILO_PCI_DEVICE" ]; then
        print_status "Found Hailo device at PCIe slot: $HAILO_PCI_DEVICE"
        echo 1 | sudo tee /sys/bus/pci/devices/0000:$HAILO_PCI_DEVICE/reset >/dev/null 2>&1 || true
        sleep 2
    else
        print_warning "No Hailo PCIe device found in lspci output"
    fi

    # Remove Hailo kernel modules
    print_status "Removing Hailo kernel modules..."
    sudo rmmod hailo_pci 2>/dev/null || true
    sudo modprobe -r hailo_pci 2>/dev/null || true

    # Clear device locks and temp files
    print_status "Clearing device locks..."
    sudo rm -f /tmp/hailo* 2>/dev/null || true
    sudo rm -f /var/lock/hailo* 2>/dev/null || true

    # CAMERA ULTRA RESET - Fix "Device or resource busy"
    print_header "📷 CAMERA ULTRA RESET 📷"
    print_status "Killing all camera processes..."
    sudo pkill -9 -f "libcamera" 2>/dev/null || true
    sudo pkill -9 -f "rpicam" 2>/dev/null || true
    sudo pkill -9 -f "picamera" 2>/dev/null || true
    sudo pkill -9 -f "gst-launch" 2>/dev/null || true

    # Unload camera modules completely
    print_status "Unloading camera modules..."
    sudo modprobe -r bcm2835_v4l2 2>/dev/null || true
    sudo modprobe -r bcm2835_mmal_vchiq 2>/dev/null || true
    sleep 3

    # Reload camera modules
    print_status "Reloading camera modules..."
    sudo modprobe bcm2835_v4l2 2>/dev/null || true
    sudo modprobe bcm2835_mmal_vchiq 2>/dev/null || true
    sleep 2

    # SHARED MEMORY CLEANUP
    print_status "Cleaning up shared memory..."
    sudo rm -f /dev/shm/pose_shm 2>/dev/null || true
    sudo rm -f /dev/shm/hand_shm 2>/dev/null || true

    # Wait for device stabilization
    print_status "Waiting for device stabilization..."
    sleep 5

    # Reload Hailo modules
    print_status "Reloading Hailo modules..."
    sudo modprobe hailo_pci 2>/dev/null || true
    sleep 3

    # Restart Hailo service
    print_status "Restarting Hailo service..."
    sudo systemctl start hailort 2>/dev/null || true
    sleep 3

    # VERIFICATION PHASE
    print_header "🔍 VERIFICATION PHASE 🔍"

    # Check Hailo device
    if command -v hailortcli > /dev/null; then
        print_status "Scanning for Hailo devices..."
        if hailortcli scan 2>/dev/null | grep -q "Hailo"; then
            print_status "✅ Hailo device detected and ready"
        else
            print_error "❌ Hailo device not detected - may need hardware check"
        fi

        # Check for open handles
        REMAINING_HANDLES=$(hailortcli list-handles 2>/dev/null | grep -c "Handle" || echo "0")
        if [ "$REMAINING_HANDLES" -eq 0 ]; then
            print_status "✅ No open Hailo handles remaining"
        else
            print_warning "⚠️  Still $REMAINING_HANDLES open handles - may cause issues"
        fi
    fi

    # Quick camera test
    print_status "Testing camera availability..."
    if [ -c "/dev/video0" ]; then
        print_status "✅ Camera device /dev/video0 available"
        # Quick libcamera test (1 second)
        if timeout 3 libcamera-hello -t 1 >/dev/null 2>&1; then
            print_status "✅ Camera test successful"
        else
            print_warning "⚠️  Camera test failed - may still be busy"
        fi
    else
        print_error "❌ No camera devices found"
    fi

    print_header "🎯 ULTRA DEEP CLEANUP COMPLETED 🎯"
    print_status "System should now be ready for clean restart"
}

# Function to show status
show_status() {
    print_header "System Status Check"
    echo ""
    
    # Check video sink configuration
    if [ -f "video_sink_config.conf" ]; then
        source video_sink_config.conf
        print_status "Video sink mode: $VIDEO_SINK"
    else
        print_warning "No video sink configuration found"
    fi
    
    echo ""
    print_header "Process Status"
    check_process "Pose Estimation Backend" "pose_estimation.py"
    check_process "Hand Pose Estimation Backend" "hand_pose_estimation.py"
    check_process "Web Server Frontend" "simple_pose_socket.py"
    check_process "Hand Ninja Game Server" "hand_ninja_game.py"
    
    echo ""
    print_header "Shared Memory Status"
    if [ -f "/dev/shm/pose_shm" ]; then
        print_status "Shared memory 'pose_shm' exists"
    else
        print_warning "Shared memory 'pose_shm' not found"
    fi
    
    echo ""
    print_header "Camera Status"
    if [ -c "/dev/video0" ]; then
        print_status "Camera devices found: $(ls /dev/video* | wc -l) devices"
    else
        print_error "No camera devices found"
    fi
}

# Trap handler for clean exit
cleanup_on_exit() {
    print_header "🛑 EMERGENCY CLEANUP TRIGGERED 🛑"
    stop_processes
    exit 0
}

# Set up trap handlers for clean exit
trap cleanup_on_exit EXIT INT TERM

# Function to start the system
start_system() {
    print_header "🚀 Starting Raspberry Pi Pose Estimation System 🚀"
    echo ""

    # ALWAYS stop any existing processes first - this ensures clean startup
    if [ "$AUTO_STOP" = true ]; then
        print_header "Cleaning up any existing processes..."
        stop_processes
        echo ""
    else
        print_warning "Skipping automatic cleanup (--no-stop was used)"
        echo ""
    fi

    # PRE-START VERIFICATION
    print_header "🔍 PRE-START VERIFICATION 🔍"

    # Verify Hailo device is available
    if command -v hailortcli > /dev/null; then
        print_status "Checking Hailo device availability..."
        if ! hailortcli scan 2>/dev/null | grep -q "Hailo"; then
            print_error "❌ Hailo device not detected!"
            print_error "Please check hardware connection or run cleanup again"
            exit 1
        else
            print_status "✅ Hailo device detected and ready"
        fi

        # Check for any remaining open handles
        OPEN_HANDLES=$(hailortcli list-handles 2>/dev/null | grep -c "Handle" || echo "0")
        if [ "$OPEN_HANDLES" -gt 0 ]; then
            print_warning "⚠️  Found $OPEN_HANDLES open Hailo handles - releasing..."
            hailortcli release-all 2>/dev/null || true
            sleep 2
        fi
    else
        print_warning "hailortcli not found - skipping Hailo verification"
    fi

    # Verify camera is available
    print_status "Checking camera availability..."
    if [ ! -c "/dev/video0" ]; then
        print_error "❌ No camera devices found!"
        exit 1
    else
        print_status "✅ Camera device available"
    fi
    
    # Start backend
    print_header "🔧 Starting Backend (Pose Estimation) 🔧"
    print_status "Camera input: $CAMERA_INPUT"
    print_status "Command: $BACKEND_SCRIPT --input $CAMERA_INPUT"

    # Clear old logs
    > backend.log
    > backend_fallback.log

    # Start backend in background
    nohup $BACKEND_SCRIPT --input $CAMERA_INPUT > backend.log 2>&1 &
    BACKEND_PID=$!

    print_status "Backend started with PID: $BACKEND_PID"
    print_status "Backend logs: $SCRIPT_DIR/backend.log"

    # Wait for shared memory to be created with better monitoring
    print_status "Waiting for shared memory to be created..."
    BACKEND_FAILED=false

    for i in {1..45}; do  # Increased timeout to 45 seconds
        # Check if backend process is still running
        if ! kill -0 $BACKEND_PID 2>/dev/null; then
            print_error "❌ Backend process died! Check backend.log for errors:"
            echo ""
            print_error "=== BACKEND LOG TAIL ==="
            tail -20 backend.log | while read line; do
                print_error "$line"
            done
            print_error "========================"
            BACKEND_FAILED=true
            break
        fi

        # Check for shared memory
        if [ -f "/dev/shm/pose_shm" ]; then
            print_status "✅ Shared memory created successfully"
            break
        fi

        # Check for specific error patterns in log
        if [ -f "backend.log" ]; then
            if grep -q "HAILO_OUT_OF_PHYSICAL_DEVICES" backend.log; then
                print_error "❌ Hailo device busy error detected!"
                print_error "Running additional cleanup..."
                kill $BACKEND_PID 2>/dev/null || true
                stop_processes
                print_error "Please run './start.sh --stop' and try again"
                exit 1
            fi

            if grep -q "Device or resource busy" backend.log; then
                print_error "❌ Camera busy error detected!"
                print_error "Running additional cleanup..."
                kill $BACKEND_PID 2>/dev/null || true
                stop_processes
                print_error "Please run './start.sh --stop' and try again"
                exit 1
            fi

            if grep -q "Segmentation fault" backend.log; then
                print_error "❌ Segmentation fault detected!"
                print_error "This indicates a serious issue - running full cleanup..."
                kill $BACKEND_PID 2>/dev/null || true
                stop_processes
                exit 1
            fi
        fi

        if [ $i -eq 45 ]; then
            print_error "❌ Timeout waiting for shared memory!"
            print_error "Backend may have failed to start properly"
            BACKEND_FAILED=true
            break
        fi

        sleep 1
        echo -n "."
    done
    echo ""

    if [ "$BACKEND_FAILED" = true ]; then
        print_error "Backend startup failed - check logs and try again"
        exit 1
    fi
    
    # Start frontend
    print_header "Starting Frontend (Web Server)..."
    print_status "Command: $FRONTEND_SCRIPT"
    
    # Start frontend in background
    nohup $FRONTEND_SCRIPT > frontend.log 2>&1 &
    FRONTEND_PID=$!
    
    print_status "Frontend started with PID: $FRONTEND_PID"
    print_status "Frontend logs: $SCRIPT_DIR/frontend.log"
    
    # Wait a moment for frontend to start
    sleep 3

    # Open browser automatically
    if [ "$OPEN_BROWSER" = true ]; then
        print_header "Opening Web Browser..."
        if command -v xdg-open > /dev/null; then
            xdg-open http://localhost:5000 > /dev/null 2>&1 &
            print_status "Browser opened automatically"
        elif command -v chromium-browser > /dev/null; then
            chromium-browser http://localhost:5000 > /dev/null 2>&1 &
            print_status "Chromium browser opened automatically"
        elif command -v firefox > /dev/null; then
            firefox http://localhost:5000 > /dev/null 2>&1 &
            print_status "Firefox browser opened automatically"
        else
            print_warning "No browser found for automatic opening"
            print_status "Please open http://localhost:5000 manually"
        fi
        sleep 1
    fi

    # Final status and verification
    echo ""
    print_header "🎯 STARTUP COMPLETE! 🎯"
    print_status "Backend PID: $BACKEND_PID"
    print_status "Frontend PID: $FRONTEND_PID"
    print_status "Web interface: http://localhost:5000"
    print_status "Logs: backend.log, frontend.log"

    # Final verification
    print_header "🔍 FINAL VERIFICATION 🔍"

    # Check if processes are still running
    if kill -0 $BACKEND_PID 2>/dev/null; then
        print_status "✅ Backend process running"
    else
        print_error "❌ Backend process died!"
    fi

    if kill -0 $FRONTEND_PID 2>/dev/null; then
        print_status "✅ Frontend process running"
    else
        print_error "❌ Frontend process died!"
    fi

    # Check shared memory
    if [ -f "/dev/shm/pose_shm" ]; then
        print_status "✅ Shared memory active"
    else
        print_warning "⚠️  Shared memory not found"
    fi

    # Check Hailo status
    if command -v hailortcli > /dev/null; then
        OPEN_HANDLES=$(hailortcli list-handles 2>/dev/null | grep -c "Handle" || echo "0")
        if [ "$OPEN_HANDLES" -eq 1 ]; then
            print_status "✅ Hailo device in use (1 handle)"
        elif [ "$OPEN_HANDLES" -gt 1 ]; then
            print_warning "⚠️  Multiple Hailo handles open ($OPEN_HANDLES)"
        else
            print_warning "⚠️  No Hailo handles open"
        fi
    fi

    echo ""
    print_header "🚀 SYSTEM READY FOR ACTION! 🚀"
    echo ""
    print_warning "Use './start.sh --stop' to stop all processes"
    print_warning "Use './start.sh --status' to check system status"
    print_warning "If restart fails, run './start.sh --stop' first!"

    # Disable trap on successful completion
    trap - EXIT INT TERM
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --camera-input)
            CAMERA_INPUT="$2"
            shift 2
            ;;
        --performance)
            ./switch_video_sink.sh performance
            print_status "Switched to performance mode (fakesink)"
            shift
            ;;
        --display)
            ./switch_video_sink.sh display
            print_status "Switched to display mode (autovideosink)"
            shift
            ;;
        --no-stop)
            AUTO_STOP=false
            print_warning "Automatic process cleanup disabled"
            shift
            ;;
        --no-browser)
            OPEN_BROWSER=false
            print_warning "Automatic browser opening disabled"
            shift
            ;;
        --stop)
            stop_processes
            exit 0
            ;;
        --status)
            show_status
            exit 0
            ;;
        --help|-h)
            echo "Raspberry Pi Pose Estimation & Hand Ninja Game Startup Script"
            echo ""
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --camera-input INPUT    Camera input (rpi, usb, /dev/videoX) [default: rpi]"
            echo "  --performance          Use fakesink (performance mode)"
            echo "  --display              Use autovideosink (display mode)"
            echo "  --no-stop              Skip automatic cleanup of existing processes"
            echo "  --no-browser           Skip automatic browser opening"
            echo "  --stop                 Stop all running processes"
            echo "  --status               Show status of running processes"
            echo "  --help                 Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                           # Start with default settings (rpi camera)"
            echo "  $0 --camera-input usb        # Start with USB camera"
            echo "  $0 --performance             # Start in performance mode"
            echo "  $0 --stop                    # Stop all processes"
            echo "  $0 --status                  # Check system status"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            echo "Use '$0 --help' for usage information"
            exit 1
            ;;
    esac
done

# Main execution
start_system
