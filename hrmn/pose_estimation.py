#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import gi, signal, sys, cv2, hailo, os, atexit
gi.require_version('Gst', '1.0')
from gi.repository import Gst, GLib

# ----------------------------------------------------------------------
# Hailo
# ----------------------------------------------------------------------
from hailo_apps_infra.hailo_rpi_common import (
    get_caps_from_pad,
    get_numpy_from_buffer,
    app_callback_class,
)
from hailo_apps_infra.pose_estimation_pipeline import GStreamerPoseEstimationApp

# ----------------------------------------------------------------------
# Shared Memory
# ----------------------------------------------------------------------
from shared_memory import init_pose_shm, POSE_SHM_NAME

MAX_PERSONS = 10
NUM_KPTS    = 17

pose_shm, pose_arr = init_pose_shm(MAX_PERSONS, NUM_KPTS)
# shape = (MAX_PERSONS,17,3)

# ----------------------------------------------------------------------
class UserAppCallback(app_callback_class):
    def __init__(self):
        super().__init__()

def app_callback(pad, info, user_data):
    buf = info.get_buffer()
    if not buf:
        return Gst.PadProbeReturn.OK

    # Frame → Array zurücksetzen
    pose_arr.fill(-1.0)

    user_data.increment()

    # Vorschau deaktivieren - keine Frame-Kopien mehr
    user_data.use_frame = False

    import hailo
    roi = hailo.get_roi_from_buffer(buf)
    detections = roi.get_objects_typed(hailo.HAILO_DETECTION)

    
        
    person_idx = 0
    for det in detections:
        if det.get_label() != "person":
            continue
        if person_idx >= MAX_PERSONS:
            break

        lms = det.get_objects_typed(hailo.HAILO_LANDMARKS)
        if not lms:
            continue
        points = lms[0].get_points()
        bb = det.get_bbox()  # normalisierte BBox

        for kp_i, p in enumerate(points):
            x_norm = p.x() * bb.width() + bb.xmin()
            y_norm = p.y() * bb.height() + bb.ymin()
            conf   = getattr(p, "score", lambda:1.)()

            pose_arr[person_idx, kp_i] = (x_norm, y_norm, conf)

            # Kein Overlay mehr - spart CPU-Zeit

        person_idx += 1

    # Keine Frame-Verarbeitung mehr nötig
    return Gst.PadProbeReturn.OK

# ----------------------------------------------------------------------
# ULTRA DEEP CLEANUP - Community proven solution
def cleanup(signum=None, frame=None):
    print(f"\n[CLEANUP] Signal {signum} received - performing ultra deep cleanup...")

    try:
        # Release Hailo handles first
        print("[CLEANUP] Releasing Hailo handles...")
        os.system("hailortcli release-all 2>/dev/null || true")
    except Exception as e:
        print(f"[CLEANUP] Error releasing Hailo handles: {e}")

    try:
        # Clean up shared memory
        print("[CLEANUP] Cleaning shared memory...")
        pose_shm.close()
        pose_shm.unlink()
        os.system("rm -f /dev/shm/pose_shm /dev/shm/hand_shm 2>/dev/null || true")
    except Exception as e:
        print(f"[CLEANUP] Error cleaning shared memory: {e}")

    print("[CLEANUP] Ultra deep cleanup completed")

    if signum is not None:
        sys.exit(0)

# Set up signal handlers for clean exit
for sig in (signal.SIGINT, signal.SIGTERM, signal.SIGHUP):
    try:
        signal.signal(sig, cleanup)
    except (OSError, ValueError):
        pass

# Also register atexit handler as backup
atexit.register(cleanup)
print("[SIGNAL] Ultra deep cleanup handlers registered")

# ----------------------------------------------------------------------
if __name__ == "__main__":
    print(f"[INFO] schreibe Pose-Daten in SHM='{POSE_SHM_NAME}' - ECHTZEIT OPTIMIERT")
    user_data = UserAppCallback()
    # Vorschau explizit deaktivieren für Performance
    user_data.use_frame = False

    # Load video sink configuration
    import os
    config_file = os.path.join(os.path.dirname(__file__), 'video_sink_config.conf')
    video_sink = "autovideosink"  # default

    if os.path.exists(config_file):
        try:
            with open(config_file, 'r') as f:
                for line in f:
                    if line.strip().startswith('VIDEO_SINK='):
                        video_sink = line.split('=')[1].strip().strip('"')
                        break
        except Exception as e:
            print(f"[CONFIG] Error reading config file: {e}")

    print(f"[CONFIG] Video sink mode: {video_sink}")

    # Apply monkey-patch only if fakesink is configured
    if video_sink == "fakesink":
        print("[CONFIG] Applying fakesink monkey-patch for performance mode")
        import gi
        gi.require_version('Gst', '1.0')
        from gi.repository import Gst

        # Originale ElementFactory-Funktion sichern
        original_make = Gst.ElementFactory.make

        def patched_make(factory_name, element_name=None):
            # Wenn autovideosink angefordert wird, gib fakesink zurück
            if factory_name == "autovideosink":
                print(f"[MONKEY-PATCH] autovideosink → fakesink umgeleitet!")
                return original_make("fakesink", element_name)
            return original_make(factory_name, element_name)

        # Monkey-Patch anwenden
        Gst.ElementFactory.make = patched_make
        print("[MONKEY-PATCH] GStreamer ElementFactory gepatcht für fakesink!")
    else:
        print("[CONFIG] Using autovideosink (display mode) - no monkey-patch needed")

    # Add architecture parameter for Hailo device
    import sys
    args = sys.argv[1:]  # Get command line arguments

    # Add --arch hailo8l if not already specified
    if '--arch' not in args:
        args.extend(['--arch', 'hailo8l'])

    # Temporarily modify sys.argv for the app
    original_argv = sys.argv[:]
    sys.argv = [sys.argv[0]] + args

    try:
        app = GStreamerPoseEstimationApp(app_callback, user_data)
        app.run()
    finally:
        sys.argv = original_argv  # Restore original argv
        cleanup()
